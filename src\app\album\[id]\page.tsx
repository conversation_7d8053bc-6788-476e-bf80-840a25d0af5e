// app/album/[id]/page.tsx

import AlbumPage from "@/features/album/components/AlbumPage";
import { client } from "@/graphql-client";
import { getArtistProfile } from "@/graphql/queries";

// Generate static params for static export
export async function generateStaticParams() {
  try {
    // Get artistId from localStorage or storage
    const getArtistId = () => {
      if (typeof window !== 'undefined') {
        return localStorage.getItem('selectedArtistId');
      }
      return null;
    };

    const artistId = getArtistId();

    if (!artistId) {
      console.log("No artistId found in storage");
      return [];
    }

    const result = await client.graphql({
      query: getArtistProfile,
      variables: {
        artistId: artistId
      },
    });

    // Process data regardless of errors
    return processAlbumParams(result);

  } catch (error: unknown) {
    // Even if GraphQL throws an error, check if we have data
    const errorWithData = error as { data?: { getArtistProfile?: unknown } };
    if (errorWithData?.data?.getArtistProfile) {
      return processAlbumParams(errorWithData);
    } else {
      console.error("True network error - no data available:", error);
      return [];
    }
  }
}

const processAlbumParams = (result: { data?: { getArtistProfile?: unknown } }) => {
  if (result?.data?.getArtistProfile) {
    type ArtistProfileType = {
      artist: {
        id: string;
        name: string;
        bio?: string | null;
        formedDate?: string | null;
        disbandedDate?: string | null;
        location?: string | null;
        __typename: string;
      };
      songs?: Array<{
        id: string;
        title?: string | null;
        duration?: number | null;
        recordID?: string | null;
        releaseDate?: string | null;
        coverPhoto?: string | null;
        role?: string | null;
        credits?: Array<{
          artistId: string;
          role: string;
          name?: string | null;
        }>;
        __typename: string;
      }>;
      albums?: Array<{
        id: string;
        releaseDate?: string | null;
        genre?: Array<string | null> | null;
        description?: string | null;
        coverArtData?: string | null;
        title?: string | null;
        __typename: string;
      }>;
      __typename: string;
    };
    const profileData = result.data.getArtistProfile as ArtistProfileType;
    if (profileData?.albums) {
      const albumParams = profileData.albums
        .filter(album => album && album.id)
        .map(album => ({ id: album.id }));
      console.log(`✅ Generated static params for ${albumParams.length} albums`);
      return albumParams;
    } else {
      console.log("❌ No albums found in artist profile");
      return [];
    }
  } else {
    console.log("❌ No artist profile data available");
    return [];
  }
};

export default function AlbumIdPage({ params }: { params: { id: string } }) {
  return <AlbumPage albumId={params.id} />;
}
