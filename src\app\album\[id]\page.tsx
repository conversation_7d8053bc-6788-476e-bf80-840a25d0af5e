// app/album/[id]/page.tsx

import AlbumPage from "@/features/album/components/AlbumPage";

type Album = {
  id: string;
};

const albums: Album[] = [
  { id: "d94ff8c3-bcf9-4ba9-90d8-74eac12b8e0c" },
  { id: "0d96e8be-d086-4f96-aae2-6fa946c50924" },
  { id: "decdad53-1b18-48a7-b217-615e0ade4e06" },
  { id: "07ed12d0-57e2-4624-a3c0-37562c538319" },
  { id: "9aba37d8-fe91-4df0-b72b-73e56a6cf006" },
  { id: "7f238f1a-6b01-458f-9a1b-3b34859018f3" },
  { id: "473abfc6-0912-466f-975d-ff0f9e06eb85" },
  { id: "7c49da66-cbd4-4f52-8295-be4bed2d242b" },
  { id: "e90dc06b-40a0-4b71-a6b8-c5ed0fdd8c65" },
  { id: "a97503b4-ea40-4f66-ae53-e075a59a23bb" },
  { id: "cde3246f-28c2-4163-afe5-45ee022a673f" },
  { id: "ff7b68e9-ba2f-4a11-8004-52f1f358856a" },
  { id: "41218f3d-e093-4c7b-b8da-e14613ae8a4e" },
  { id: "500d9d4a-351b-420a-9ad6-a2a83e59345c" },
];

export function generateStaticParams(): { id: string } [] {
  return albums.map((album) => {
    return { id: album.id }
  }  );
}

export default function AlbumIdPage({ params }: { params: { id: string } }) {
  return <AlbumPage albumId={params.id} />;
}
