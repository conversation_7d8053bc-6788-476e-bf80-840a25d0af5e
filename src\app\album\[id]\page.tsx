// app/album/[id]/page.tsx
'use client'

import AlbumPage from "@/features/album/components/AlbumPage";
import { useEffect, useState } from "react";
import { client } from "@/graphql-client";
import { getAlbum } from "@/graphql/queries";

export default function AlbumIdPage({ params }: { params: { id: string } }) {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Get albumId from localStorage or use params.id as fallback
    const getAlbumId = () => {
      if (typeof window !== 'undefined') {
        const storedAlbumId = localStorage.getItem('selectedAlbumId');
        return storedAlbumId || params.id;
      }
      return params.id;
    };

    const albumId = getAlbumId();

    const fetchAlbum = async () => {
      setLoading(true);
      try {
        const result = await client.graphql({
          query: getAlbum,
          variables: {
            albumId: albumId
          },
        });

        // Process data regardless of errors
        processAlbumData(result);

      } catch (error: unknown) {
        // Even if GraphQL throws an error, check if we have data
        const errorWithData = error as { data?: { getAlbum?: unknown } };
        if (errorWithData?.data?.getAlbum) {
          processAlbumData(errorWithData);
        } else {
          console.error("True network error - no data available:", error);
        }
      } finally {
        setLoading(false);
      }
    };

    const processAlbumData = (result: { data?: { getAlbum?: unknown } }) => {
      if (result?.data?.getAlbum) {
        type AlbumType = {
          id: string;
          title?: string | null;
          releaseDate?: string | null;
          genre?: Array<string | null> | null;
          description?: string | null;
          coverArtData?: string | null;
          tracks?: Array<{
            id: string;
            trackPosition?: number | null;
            __typename: string;
          }>;
          __typename: string;
        };
        const albumInfo = result.data.getAlbum as AlbumType;
        if (albumInfo?.id) {
          console.log("✅ Successfully loaded album:", albumInfo);
        } else {
          console.log("❌ Album not found in data");
        }
      } else {
        console.log("❌ No album data available");
      }
    };

    if (albumId) fetchAlbum();
  }, [params.id]);

  if (loading) return <div className="p-8">Loading album...</div>;

  return <AlbumPage albumId={params.id} />;
}
