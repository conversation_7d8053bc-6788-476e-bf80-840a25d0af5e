// app/album/[id]/page.tsx

import AlbumPage from "@/features/album/components/AlbumPage";
import { client } from "@/graphql-client";
import { getAllArtist, getArtistProfile } from "@/graphql/queries";

// Generate static params for static export
export async function generateStaticParams() {
  try {
    console.log("🚀 Starting album static params generation...");

    // Step 1: Get all artists
    const artistsResult = await client.graphql({
      query: getAllArtist,
      variables: {},
    });

    const artists = artistsResult?.data?.getAllArtist || [];
    console.log(`✅ Found ${artists.length} artists`);

    if (artists.length === 0) {
      console.log("❌ No artists found, returning empty array");
      return [];
    }

    // Step 2: Collect all album IDs from all artists
    const allAlbumIds: string[] = [];

    // Process each artist sequentially to avoid API rate limits
    for (let i = 0; i < artists.length; i++) {
      const artist = artists[i];

      if (!artist || !artist.id) {
        console.log(`⚠️ Skipping invalid artist at index ${i}`);
        continue;
      }

      try {
        console.log(`📡 Fetching albums for artist ${i + 1}/${artists.length}: ${artist.id}`);

        const profileResult = await client.graphql({
          query: getArtistProfile,
          variables: { artistId: artist.id },
        });

        const artistProfile = profileResult?.data?.getArtistProfile;
        const albums = artistProfile?.albums || [];

        console.log(`📀 Found ${albums.length} albums for artist ${artist.id}`);

        // Extract album IDs and add to collection
        albums.forEach((album) => {
          if (album && album.id) {
            allAlbumIds.push(album.id);
            console.log(`➕ Added album: ${album.id}`);
          }
        });

      } catch (error) {
        console.error(`❌ Failed to fetch albums for artist ${artist.id}:`, error);
        continue;
      }

      // Add small delay to avoid overwhelming the API
      if (i < artists.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Step 3: Remove duplicates and create final params
    const uniqueAlbumIds = [...new Set(allAlbumIds)];
    const albumParams = uniqueAlbumIds.map(id => ({ id: id }));

    console.log(`✅ Generated static params for ${albumParams.length} unique albums`);
    console.log(`📋 Album IDs:`, uniqueAlbumIds.slice(0, 10), uniqueAlbumIds.length > 10 ? '...' : '');

    return albumParams;

  } catch (error: unknown) {
    console.error("💥 Error generating album static params:", error);
    return [];
  }
}



export default function AlbumIdPage({ params }: { params: { id: string } }) {
  return <AlbumPage albumId={params.id} />;
}
