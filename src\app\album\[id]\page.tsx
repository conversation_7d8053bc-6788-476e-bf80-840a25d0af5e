// app/album/[id]/page.tsx

import AlbumPage from "@/features/album/components/AlbumPage";
import { client } from "@/graphql-client";
import { getAllArtist, getArtistProfile } from "@/graphql/queries";

// Generate static params for static export
export async function generateStaticParams() {
  console.log("🚀 Starting album static params generation...");

  let artists: { id: string }[] = [];

  try {
    // Step 1: Get all artists
    const artistsResult = await client.graphql({
      query: getAllArtist,
      variables: {},
    });
    artists = artistsResult?.data?.getAllArtist || [];
  } catch (error: unknown) {
    // Handle GraphQL errors but still use data if available
    const errorWithData = error as { data?: { getAllArtist?: { id: string }[] }; message?: string };
    console.log("⚠️ GraphQL error but checking for data:", errorWithData.message);
    if (errorWithData?.data?.getAllArtist) {
      artists = errorWithData.data.getAllArtist;
      console.log(`✅ Using data from error response: ${artists.length} artists`);
    } else {
      console.error("💥 No data available:", error);
      return [];
    }
  }

  console.log(`✅ Found ${artists.length} artists`);

  if (artists.length === 0) {
    console.log("❌ No artists found, returning empty array");
    return [];
  }

  // Step 2: Collect all album IDs from all artists
  const allAlbumIds: string[] = [];

  // Process each artist sequentially
  for (let i = 0; i < artists.length; i++) {
    const artist = artists[i];

    if (!artist || !artist.id) {
      continue;
    }

    try {
      const profileResult = await client.graphql({
        query: getArtistProfile,
        variables: { artistId: artist.id },
      });

      const albums = profileResult?.data?.getArtistProfile?.albums || [];

      // Extract album IDs and add to collection
      albums.forEach((album) => {
        if (album && album.id) {
          allAlbumIds.push(album.id);
        }
      });

    } catch (error: unknown) {
      // Check if error has data
      const errorWithData = error as { data?: { getArtistProfile?: { albums?: { id?: string }[] } } };
      if (errorWithData?.data?.getArtistProfile?.albums) {
        const albums = errorWithData.data.getArtistProfile.albums;
        albums.forEach((album: { id?: string }) => {
          if (album && album.id) {
            allAlbumIds.push(album.id);
          }
        });
      }
      continue;
    }
  }

  // Step 3: Remove duplicates and create final params
  const uniqueAlbumIds = [...new Set(allAlbumIds)];
  const albumParams = uniqueAlbumIds.map(id => ({ id }));

  console.log(`✅ Generated static params for ${albumParams.length} unique albums`);

  return albumParams;
}



export default function AlbumIdPage({ params }: { params: { id: string } }) {
  return <AlbumPage albumId={params.id} />;
}
