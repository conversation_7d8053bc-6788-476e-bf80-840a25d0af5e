// app/album/[id]/page.tsx

import AlbumPage from "@/features/album/components/AlbumPage";
import { client } from "@/graphql-client";
import { getAllArtist, getArtistProfile } from "@/graphql/queries";

// Generate static params for static export
export async function generateStaticParams() {
  try {
    // First, get all artists
    const artistsResult = await client.graphql({
      query: getAllArtist,
      variables: {},
    });

    const artists = artistsResult?.data?.getAllArtist || [];
    console.log(`✅ Found ${artists.length} artists for album generation`);

    // Collect all album IDs from all artists
    const allAlbumIds = new Set<string>();

    // Process artists in batches to avoid overwhelming the API
    const batchSize = 10;
    for (let i = 0; i < artists.length; i += batchSize) {
      const batch = artists.slice(i, i + batchSize);

      const batchPromises = batch.map(async (artist: { id: string }) => {
        if (!artist?.id) return [];

        try {
          const profileResult = await client.graphql({
            query: getArtistProfile,
            variables: { artistId: artist.id },
          });

          const albums = profileResult?.data?.getArtistProfile?.albums || [];
          return albums
            .filter((album): album is NonNullable<typeof album> => album != null && album.id != null)
            .map((album) => album.id);
        } catch (error) {
          console.warn(`Failed to fetch albums for artist ${artist.id}:`, error);
          return [];
        }
      });

      const batchResults = await Promise.all(batchPromises);
      batchResults.flat().forEach(albumId => allAlbumIds.add(albumId));

      console.log(`✅ Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(artists.length/batchSize)}, total albums: ${allAlbumIds.size}`);
    }

    const albumParams = Array.from(allAlbumIds).map(id => ({ id }));
    console.log(`✅ Generated static params for ${albumParams.length} unique albums`);
    return albumParams;

  } catch (error: unknown) {
    console.error("Error generating album static params:", error);
    return [];
  }
}



export default function AlbumIdPage({ params }: { params: { id: string } }) {
  return <AlbumPage albumId={params.id} />;
}
