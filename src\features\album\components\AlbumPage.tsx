'use client'
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Play,
  Heart,
  Edit3,
  Share2,
  Clock,
  Calendar,
  Music,
  MoreHorizontal,
  Award,
  ListMusic,
  ExternalLink,
  Disc,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { client } from "@/graphql-client"
import { getAlbum } from "@/graphql/queries"
import type { GetAlbumQuery, GetAlbumQueryVariables } from "@/API"

export default function AlbumPage({ albumId }: { albumId?: string }) {
  const router = useRouter();
  const [album, setAlbum] = useState<GetAlbumQuery["getAlbum"] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Restore handlers for navigation
  const handleSongClick = () => {
    // TODO: Use track id if available
    router.push(`/song`);
  };
  const handlePlaylistClick = () => {
    router.push(`/playlist`);
  };
  const handleArtistClick = () => {
    router.push(`/artist`);
  };

  // Use the albumId parameter passed from the page component
  const id = albumId;

  useEffect(() => {
    if (!id) return;
    setLoading(true);
    client.graphql({
      query: getAlbum,
      variables: { albumId: id } as GetAlbumQueryVariables,
    })
      .then((res: { data?: GetAlbumQuery }) => {
        setAlbum(res.data?.getAlbum || null);
        setError(null);
      })
      .catch(() => {
        setError("Failed to load album");
        setAlbum(null);
      })
      .finally(() => setLoading(false));
  }, [id]);

  // Replace static albumTracks with fetched data
  const albumTracks = album?.tracks?.map((track, idx) => ({
    id: idx + 1,
    title: `Track ${track?.trackPosition ?? idx + 1}`,
    duration: "-",
    performers: "-",
  })) || [];

  if (loading) return <div className="p-8">Loading album...</div>;
  if (error) return <div className="p-8 text-red-500">{error}</div>;

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header / Hero Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Album Cover */}
          <div className="flex-shrink-0">
            <Image
              src={album?.coverArtData || "/dummy-image.png?height=300&width=300"}
              alt="Album Cover"
              width={300}
              height={300}
              className="rounded-lg shadow-lg"
            />
          </div>

          {/* Album Info */}
          <div className="flex-1 space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Badge variant="outline" className="w-fit">
                  Album
                </Badge>
                <h1 className="text-4xl md:text-5xl font-bold">{album?.title || "-"}</h1>
              </div>

              <div className="flex flex-wrap items-center gap-2">
                <span className="text-xl text-muted-foreground">by</span>
                <span className="text-xl text-primary cursor-pointer hover:underline font-medium" onClick={()=>handleArtistClick()}>Artist</span>
              </div>

              <p className="text-lg text-muted-foreground max-w-2xl">
                {album?.description || "-"}
              </p>

              <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{album?.releaseDate || "-"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Music className="w-4 h-4" />
                  <span>{albumTracks.length} tracks</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>-</span>
                </div>
              </div>

              {/* Genres */}
              <div className="flex flex-wrap gap-2">
                {(album?.genre || []).map((g, i) => g && <Badge key={i} variant="secondary">{g}</Badge>)}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button size="lg" className="gap-2">
                <Play className="w-5 h-5" />
                Play All
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Heart className="w-5 h-5" />
                Like (1,247)
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Edit3 className="w-5 h-5" />
                Edit
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Share2 className="w-5 h-5" />
                Share
              </Button>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Tracklist */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Disc className="w-5 h-5" />
                  Tracklist
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {/* Table Header */}
                  <div className="grid grid-cols-12 gap-4 px-4 py-2 text-sm font-medium text-muted-foreground border-b">
                    <div className="col-span-1">#</div>
                    <div className="col-span-6">Title</div>
                    <div className="col-span-3">Performer(s)</div>
                    <div className="col-span-2">Duration</div>
                  </div>

                  {/* Track Rows */}
                  {albumTracks.map((track, index) => (
                    <div
                      key={track.id}
                      className="grid grid-cols-12 gap-4 px-4 py-3 rounded-lg hover:bg-muted/50 group cursor-pointer"
                    >
                      <div className="col-span-1 flex items-center">
                        <span className="text-muted-foreground group-hover:hidden">{index + 1}</span>
                        <Play className="w-4 h-4 hidden group-hover:block" />
                      </div>

                      <div className="col-span-6 flex items-center">
                        <div>
                          <p className="font-medium text-primary cursor-pointer hover:underline" onClick={()=>handleSongClick()}>{track.title}</p>
                        </div>
                      </div>

                      <div className="col-span-3 flex items-center">
                        <span className="text-sm text-muted-foreground">{track.performers}</span>
                      </div>

                      <div className="col-span-2 flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">{track.duration}</span>
                        <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Credits */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Album Credits
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">John Doe</p>
                      <p className="text-sm text-muted-foreground">Executive Producer, Primary Artist</p>
                    </div>
                    <Button onClick={()=>handleArtistClick()} variant="ghost" size="sm">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">Sarah Mitchell</p>
                      <p className="text-sm text-muted-foreground">Co-Producer, Vocal Producer</p>
                    </div>
                    <Button onClick={()=>handleArtistClick()} variant="ghost" size="sm">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">Alex Thompson</p>
                      <p className="text-sm text-muted-foreground">Mixing Engineer</p>
                    </div>
                    <Button onClick={()=>handleArtistClick()} variant="ghost" size="sm">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">Emma Wilson</p>
                      <p className="text-sm text-muted-foreground">Mastering Engineer</p>
                    </div>
                    <Button onClick={()=>handleArtistClick()} variant="ghost" size="sm">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">Sunset Studios</p>
                      <p className="text-sm text-muted-foreground">Recording Studio</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Album Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Album Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Likes</span>
                  <span className="font-medium">1,247</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Plays</span>
                  <span className="font-medium">45,892</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Playlist Adds</span>
                  <span className="font-medium">2,156</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Peak Chart Position</span>
                  <span className="font-medium">#12</span>
                </div>
              </CardContent>
            </Card>

            {/* Appears in Playlists */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ListMusic className="w-5 h-5" />
                  Featured In Playlists
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                      <Music className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">Indie Rock Hits</p>
                      <p className="text-sm text-muted-foreground">3 tracks</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={()=>handlePlaylistClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                      <Music className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">Alternative Vibes</p>
                      <p className="text-sm text-muted-foreground">2 tracks</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={()=>handlePlaylistClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                      <Music className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">New Discoveries</p>
                      <p className="text-sm text-muted-foreground">4 tracks</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={()=>handlePlaylistClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Album Info */}
            <Card>
              <CardHeader>
                <CardTitle>Album Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Label</p>
                  <span className="text-sm text-muted-foreground">Independent</span>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Format</p>
                  <span className="text-sm text-muted-foreground">Digital, Vinyl</span>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Catalog Number</p>
                  <span className="text-sm text-muted-foreground">JD-2023-001</span>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Copyright</p>
                  <span className="text-sm text-muted-foreground">℗ 2023 John Doe Music</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Manager Tools */}
        <Card className="mt-8 border-dashed border-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-muted-foreground">
              <Edit3 className="w-5 h-5" />
              Album Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <Button variant="outline" className="justify-start">
                <Edit3 className="w-4 h-4 mr-2" />
                Edit Album Info
              </Button>
              <Button variant="outline" className="justify-start">
                <Music className="w-4 h-4 mr-2" />
                Manage Tracklist
              </Button>
              <Button variant="outline" className="justify-start">
                <Award className="w-4 h-4 mr-2" />
                Edit Credits
              </Button>
              <Button variant="outline" className="justify-start">
                <Calendar className="w-4 h-4 mr-2" />
                Update Release Info
              </Button>
              <Button variant="outline" className="justify-start">
                <Disc className="w-4 h-4 mr-2" />
                Change Cover Art
              </Button>
              <Button variant="outline" className="justify-start">
                <Share2 className="w-4 h-4 mr-2" />
                Distribution Settings
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
